import 'package:flutter_metatel/core/utils/app_log.dart';
import 'package:get/get.dart';

import '../../data/models/user_status.dart';
import '../../data/services/user_status_service.dart';

class UserStatusController extends GetxController {
  late final UserStatusService _userStatusService;

  final Rx<UserStatus> _userStatus = UserStatus.normalUser().obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  // final RxBool _isInitialized = false.obs;

  // Getters
  UserStatus get userStatus => _userStatus.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  bool get hasError => _errorMessage.value.isNotEmpty;
  // bool get isInitialized => _isInitialized.value;

  bool get isMember => userStatus.isMember && !userStatus.isMembershipExpired;
  bool get isNormalUser => !isMember;
  // bool get isNotLoggedIn => userStatus.isNotLoggedIn;
  // bool get isLoggedIn => userStatus.isLoggedIn;

  @override
  void onInit() {
    super.onInit();
    _initController();
  }

  Future<void> _initController() async {
    try {
      // 获取 Service 实例
      _userStatusService = Get.find<UserStatusService>();

      // // 检查 Service 是否准备好
      // if (!_userStatusService.isReady) {
      //   throw Exception('UserStatusService 尚未准备好');
      // }

      // 加载本地用户状态
      await loadLocalUserStatus();

      // _isInitialized.value = true;
      // print('✅ UserStatusController 初始化完成');
    } catch (e) {
      // print('❌ UserStatusController 初始化失败: $e');
      _setError('控制器初始化失败: $e');
    }
  }

  Future<void> fetchUserStatus() async {
    // if (!isInitialized) {
    //   _setError('控制器尚未初始化完成');
    //   return;
    // }

    try {
      _setLoading(true);
      _clearError();

      final status = await _userStatusService.fetchUserStatusFromApi();
      await _updateUserStatus(status);
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadLocalUserStatus() async {
    try {
      final localStatus = await _userStatusService.loadUserStatusFromLocal();
      if (localStatus != null) {
        _userStatus.value = localStatus;
      }
    } catch (e) {
      AppLogger.e('❌ 加载本地用户状态失败: $e');
    }
  }

  Future<void> _updateUserStatus(UserStatus newStatus) async {
    _userStatus.value = newStatus;
    await _userStatusService.saveUserStatusToLocal(newStatus);
  }

  void _setLoading(bool loading) => _isLoading.value = loading;
  void _setError(String error) => _errorMessage.value = error;
  void _clearError() => _errorMessage.value = '';
}
