import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/home_appbar_base.dart';
// import 'package:flutter_metatel/app/modules/home/<USER>/mine_view.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/set/set_oversea.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/setmyselfinfo/set_my_self_info.dart';
import 'package:flutter_metatel/app/widgets/avatar_frame.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../core/utils/util.dart';
import '../../../../core/values/colors.dart';
import '../../../../r.dart';
import '../../../../routes/pages.dart';
// import '../../../widgets/divider_cus.dart';
import '../../../widgets/mavatar_circle_avatar.dart';
import '../../../widgets/ota_update/update.dart';
import '../../../widgets/wooden_setting_item.dart';
import '../../base/base_view.dart';
import '../appbar/appbar.dart';
import 'mine_controller.dart';
import 'myqrcode/my_qrcode_page_im.dart';
import 'realname/real_name_page.dart';

class MineViewImOverSea extends StatefulWidget {
  const MineViewImOverSea({super.key});

  @override
  State<StatefulWidget> createState() => _MineViewImOverSeaState();
}

class _MineViewImOverSeaState extends State<MineViewImOverSea> {
  final MineController controller = Get.put(MineController(), permanent: true);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return createAppBar(
      title: L.main_mine.tr,
      type: SearchType.square,
      // canSearch: true,
      // showTitle: true,
      // showContactor: false,
      isHome: true,
      suffix: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              Get.to(
                () => MyQrcodePageIm(
                  controller: controller,
                ),
              );
            },
            child: Container(
              width: 44.r,
              height: 52.r,
              // padding: const EdgeInsets.all(8).r,
              child: Center(
                child: Image.asset(
                  R.iconMineOverseaQr,
                  width: 20.r,
                  height: 20.r,
                  color: AppColors.colorFFCAB692,
                ),
              ),
            ),
          ),
          // SizedBox(width: 4.r),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              controller.resetWalletOpen();
              Get.to(() => SetOverSeaPage(controller));
            },
            child: Container(
              width: 44.r,
              height: 52.r,
              // padding: const EdgeInsets.all(8).r,
              child: Center(
                child: Image.asset(
                  R.iconMineOverseaSet,
                  width: 20.r,
                  height: 20.r,
                  color: AppColors.colorFFCAB692,
                ),
              ),
            ),
          ),
          // SizedBox(width: 3.r),
        ],
      ),
      body: Obx(
        () {
          return BaseView(
              isDark: false,
              Scaffold(
                // backgroundColor: AppColors.backgroundGray,
                body: Container(
                  height: 1.sh,
                  width: 1.sw,
                  // decoration: BoxDecoration(
                  //   color: Colors.white.withOpacity(0.9),
                  //   image: DecorationImage(image: AssetImage(R.meBg), fit: BoxFit.cover),
                  // ),
                  child: Stack(
                    children: [
                      /// 背景图
                      Container(
                        height: 1.sh,
                        width: 1.sw,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          image: DecorationImage(image: AssetImage(R.meBg), fit: BoxFit.cover),
                        ),
                      ),

                      /// 背景Icon
                      Center(
                        child: Image.asset(
                          R.meBgIcon,
                          fit: BoxFit.fill,
                          width: 359.3.r,
                          height: 257.09.r,
                        ),
                      ),
                      ScrollConfiguration(
                        behavior: ScrollBehavior().copyWith(overscroll: false),
                        child: SingleChildScrollView(
                          child: SizedBox(
                            height: 786.18.r,
                            // width: 1.sw,
                            child: Stack(
                              children: [
                                /// 背景链条左
                                Positioned(
                                  top: 0,
                                  left: 1.sw / 2 - 115.38.r,
                                  child: Image.asset(
                                    R.meChain,
                                    fit: BoxFit.fill,
                                    height: 678.18.r,
                                    // height: 592.18.r,
                                  ),
                                ),

                                /// 背景链条右
                                Positioned(
                                  top: 0,
                                  right: 1.sw / 2 - 131.27.r,
                                  child: Image.asset(
                                    R.meChain,
                                    fit: BoxFit.fill,
                                    height: 678.18.r,
                                    // height: 592.18.r,
                                  ),
                                ),

                                Align(
                                  alignment: Alignment.topCenter,
                                  child: Column(
                                    // padding: EdgeInsets.zero,
                                    children: [
                                      SizedBox(height: 55.08.r),

                                      /// 用户信息板
                                      _buildInfoBoard(),

                                      SizedBox(height: 30.53.r),

                                      /// 设置按钮区
                                      Container(
                                        width: 348.74.r,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            /// 游戏工会
                                            _buildExtraLeftSpacing(
                                              leftSpacing: 8.88.r,
                                              child: WoodenSettingItem(
                                                L.game_center.tr,
                                                image: R.settingGameCenterIcon,
                                                onTap: () => Get.toNamed(Routes.GameCenterMemberView),
                                              ),
                                            ),
                                            SizedBox(height: 24.44.r),

                                            /// 分享推广
                                            _buildExtraLeftSpacing(
                                              leftSpacing: 22.38.r,
                                              child: WoodenSettingItem(
                                                L.share_promotion.tr,
                                                image: R.settingShareIcon,
                                                onTap: () => Get.toNamed(Routes.InvitePage),
                                              ),
                                            ),
                                            SizedBox(height: 24.44.r),

                                            /// 实名认证
                                            _buildExtraLeftSpacing(
                                              leftSpacing: 8.88.r,
                                              child: WoodenSettingItem(
                                                L.real_authenticate.tr,
                                                image: R.settingGameCenterIcon,
                                                onTap: () => Get.to(RealNamePage()),
                                                suffix: Obx(
                                                  () => Text(
                                                    controller.readAuthenticationStatus.value
                                                        ? L.real_name_pass_auth.tr
                                                        : L.real_name_no_auth.tr,
                                                    style: TextStyle(
                                                      fontSize: 12.sp,
                                                      fontWeight: FontWeight.w400,
                                                      color: AppColors.colorFFE6E1DD,
                                                    ),
                                                    maxLines: 1,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 24.44.r),

                                            /// 黑名单
                                            _buildExtraLeftSpacing(
                                              child: WoodenSettingItem(
                                                L.blacklist.tr,
                                                image: R.iconMineOverseaBlock,
                                                onTap: () => Get.toNamed(Routes.Blacklist),
                                              ),
                                            ),
                                            SizedBox(height: 24.44.r),

                                            /// 数据备份与恢复
                                            _buildExtraLeftSpacing(
                                              leftSpacing: 11.88.r,
                                              child: WoodenSettingItem(
                                                L.data_backup_recover.tr,
                                                image: R.icoDataBackup,
                                                onTap: () => controller.onBuckup(),
                                              ),
                                            ),
                                            SizedBox(height: 24.44.r),

                                            /// 应用版本
                                            _buildExtraLeftSpacing(
                                              leftSpacing: 19.r,
                                              child: WoodenSettingItem(
                                                L.main_version_update.tr,
                                                image: R.iconMineOverseaVersion,
                                                onTap: () => OTAUtil().checkVersion(UpdateType.setting),
                                                showSuffixArrow: false,
                                                suffix: Text(
                                                  controller.versionName.value,
                                                  style: TextStyle(
                                                    fontSize: 11.sp,
                                                    fontWeight: FontWeight.w400,
                                                    color: AppColors.colorFFE6E1DD,
                                                  ),
                                                  maxLines: 1,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ));
        },
      ),
    );
  }

  Widget _buildInfoBoard() {
    return GestureDetector(
      onTap: () {
        Get.to(() => const SetMySelfInfoPage());
      },
      child: Container(
        width: 329.285.r,
        height: 101.95.r,
        child: Stack(
          children: [
            /// 背景板
            Positioned(
              top: 0,
              left: 49.285.r,
              child: Image.asset(
                R.meInfoBoard,
                width: 280.r,
                height: 101.95.r,
              ),
            ),

            /// 背景内板
            Positioned(
              top: 0,
              bottom: 0,
              left: 49.285.r,
              child: Image.asset(
                R.meInfoBoardInner,
                width: 275.r,
                height: 82.39.r,
              ),
            ),

            /// 头像
            Positioned(
              top: 0,
              left: 0,
              child: GestureDetector(
                onTap: () {
                  showBottomDialogCommonWithCancel(
                    context,
                    widgets: controller.buildSetHeadBottomSheetItemWidgets(context),
                  );
                },
                child: Stack(
                  children: [
                    /// 头像
                    /// 留给child的影子显示的空间
                    SizedBox(
                      width: 110.r,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: AvatarFrame(
                          child: Center(
                            child: MAvatarCircle(
                              diameter: 76.57,
                              text: controller.userLocalName.value,
                              imagePath: controller.userIconPath.value,
                            ),
                          ),
                        ),
                      ),
                    ),

                    /// 可编辑图标
                    Positioned(
                      bottom: 3.r,
                      right: 1.44.r + 11.43.r, // + 多留给影子的空间
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.colorFF28231A,
                              blurRadius: 6.r,
                              offset: Offset(0, 3.r),
                            ),
                          ],
                        ),
                        child: Image.asset(
                          R.meAvatarEditable,
                          width: 36.r,
                          height: 36.r,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            /// 用户名、认证信息
            Positioned(
                top: 0,
                bottom: 0,
                left: 111.7.r,
                right: 10.r,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      controller.userLocalName.value,
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w900,
                        color: AppColors.colorFF28231A,
                        height: 1.3,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        Obx(
                          () => _buildWoodenButton(
                            controller.readAuthenticationStatus.value
                                ? L.real_name_pass_auth.tr
                                : L.real_name_no_auth.tr,
                          ),
                        ),
                        Obx(
                          () => controller.isMember.value
                              ? _buildWoodenButton(L.game_center_member.tr)
                              : SizedBox.shrink(),
                        ),
                      ],
                    ),
                  ],
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildWoodenButton(String text) {
    return Container(
      height: 29.r,
      constraints: BoxConstraints(minWidth: 73.r),
      padding: EdgeInsets.symmetric(horizontal: 19.r),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(R.tabbarTabActiveBg),
          fit: BoxFit.fill,
        ),
      ),
      child: Center(
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 11.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.colorFFFFC670,
          ),
        ),
      ),
    );
  }

  Widget _buildExtraLeftSpacing({required Widget child, double leftSpacing = 0}) {
    return Padding(
      padding: EdgeInsets.only(
        left: leftSpacing,
      ),
      child: child,
    );
  }
}
