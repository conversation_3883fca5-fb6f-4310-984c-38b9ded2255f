import 'dart:convert';

import 'package:get/get.dart';

import '../../../../core/utils/app_log.dart';
import '../../../../meeting/meeting_helper.dart';
import '../../../../routes/pages.dart';
import '../../../data/services/config_service.dart';
import '../../square/bean/square_banner_bean.dart';
import '../../userStatus/user_status_controller.dart';

class GameCenterController extends GetxController {
  RxInt selectedGameIndex = 0.obs;
  var config = Get.find<AppConfigService>();
  RxList<GameInfo> gameInfos = <GameInfo>[].obs;
  RxBool isMember = Get.find<UserStatusController>().isMember.obs;

  List<GameInfo> mockApiData = [
    GameInfo(
      id: '1',
      name: '夜鸦游戏代练工会',
      image: 'https://www.bigfootdigital.co.uk/wp-content/uploads/2020/07/image-optimisation-scaled.jpg',
      description: '游戏1描述',
      banners: [
        SquareBannerBean(
            type: 0,
            image:
                "https://image-processor-storage.s3.us-west-2.amazonaws.com/images/866759932dc5358cee86f6552d1250f2/inside-bubble-spheres.jpg"),
        SquareBannerBean(
            type: 0,
            image:
                "https://image-processor-storage.s3.us-west-2.amazonaws.com/images/866759932dc5358cee86f6552d1250f2/inside-bubble-spheres.jpg"),
      ],
      meetingUrl: "https://meeting.linksay.center/?id=aa6f593bbf7649c59ee40a6b2af1545f",
      articles: [
        Article(
          title: "夜鸦游戏课程，代练游戏， 赚取收益",
          createdAt: "2025/05/05",
          isMember: false,
          isPin: true,
          content:
              "这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。\n\n 这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。",
          videoThumbnail: "https://storage.needpix.com/rsynced_images/dark-2590141_1280.jpg",
          videoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
        ),
        Article(
          title: "夜鸦游戏课程，代练游戏， 赚取收益",
          createdAt: "2025/05/11",
          isMember: true,
          isPin: false,
          imageUrls: [
            "https://cdn.pixabay.com/photo/2018/08/04/11/30/draw-3583548_1280.png",
            "https://cdn.pixabay.com/photo/2021/08/10/01/38/couple-6534645_1280.jpg",
            "https://cdn.pixabay.com/photo/2021/08/10/01/38/couple-6534645_1280.jpg",
            "https://cdn.pixabay.com/photo/2021/08/10/01/38/couple-6534645_1280.jpg",
          ],
          content:
              "这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。\n\n 这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。\n\n 这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。\n\n 这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。这里是正文内容，中文内容填充文案。",
        ),
        Article(
          title: "夜鸦游戏课程，代练游戏， 赚取收益",
          createdAt: "2025/05/25",
          isMember: true,
          isPin: false,
          imageUrls: ["https://cdn.pixabay.com/photo/2021/08/10/01/38/couple-6534645_1280.jpg"],
        ),
      ].obs,
    ),
    GameInfo(
      id: '2',
      name: '游戏2',
      description: '游戏2描述',
      banners: [],
    ),
    GameInfo(
      id: '3',
      name: '游戏3',
      image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
      banners: [],
    ),
    GameInfo(
      id: '4',
      name: '游戏4',
      description: '游戏4描述',
      banners: [],
    ),
    GameInfo(
      id: '5',
      name: '游戏5',
      image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
      description: '游戏5描述',
      banners: [
        SquareBannerBean(
            type: 0, image: "https://img.freepik.com/premium-photo/bubble-with-word-bubble-middle_895799-6921.jpg"),
        SquareBannerBean(
            type: 0, image: "https://img.freepik.com/premium-photo/bubble-with-word-bubble-middle_895799-6921.jpg"),
      ],
      meetingUrl: "test",
    ),
    GameInfo(
      id: '6',
      name: '游戏6',
      image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
      description: '游戏6描述',
      banners: [],
    ),
    GameInfo(
      id: '7',
      name: '游戏7',
      image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
      description: '游戏7描述',
      banners: [],
    ),
    GameInfo(
      id: '8',
      name: '游戏8',
      image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
      description: '游戏8描述',
      banners: [],
    ),
    GameInfo(
      id: '9',
      name: '游戏9',
      image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
      description: '游戏9描述',
      banners: [],
    ),
    GameInfo(
      id: '10',
      name: '游戏10',
      image: 'https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg',
      description: '游戏10描述',
      banners: [],
    ),
  ];

  @override
  void onInit() {
    super.onInit();
    loadData();
    // sortArticles(gameInfos[0].articles!);
  }

  // 加载数据（优先使用本地缓存）
  Future<void> loadData() async {
    // 先尝试从本地存储加载
    final savedData = config.readGameCenterData();

    if (savedData != null) {
      try {
        final List<dynamic> jsonData = json.decode(savedData);
        gameInfos.value = jsonData.map((item) => GameInfo.fromJson(item)).toList();
      } catch (e) {
        AppLogger.e("Error Catch<loadData>: $e");
      }
    }

    // 然后从API获取最新数据
    await fetchDataFromAPI();
  }

  // 从API获取数据
  Future<void> fetchDataFromAPI() async {
    try {
      // isLoading.value = true;

      // 替换为你的API端点

      // if (response.statusCode == 200) {
      //   final List<dynamic> jsonData = json.decode(response.body);
      //   final List<ListItem> apiItems = jsonData
      //       .map((item) => ListItem.fromJson(item))
      //       .toList();
      List<GameInfo> apiItems = mockApiData.obs;
      // 应用本地保存的顺序
      applySavedOrder(apiItems);
      // }
    } catch (e) {
      AppLogger.e("Error Catch<fetchDataFromAPI>: $e");
    } finally {
      // isLoading.value = false;
    }
  }

  // 应用本地保存的顺序
  void applySavedOrder(List<GameInfo> apiItems) {
    final savedData = config.readGameCenterData();

    if (savedData != null) {
      try {
        final List<dynamic> savedOrder = json.decode(savedData);
        final List<String> savedIds = savedOrder.map((item) => item['id'].toString()).toList();

        // 按照保存的顺序重新排列
        List<GameInfo> orderedItems = [];

        // 先添加保存顺序中存在的项目
        for (String id in savedIds) {
          final item = apiItems.firstWhereOrNull((item) => item.id == id);
          if (item != null) {
            orderedItems.add(item);
          }
        }

        // 再添加新增的项目（不在保存顺序中的）
        for (GameInfo item in apiItems) {
          if (!savedIds.contains(item.id)) {
            orderedItems.add(item);
          }
        }

        gameInfos.value = orderedItems;
      } catch (e) {
        gameInfos.value = apiItems;
      }
    } else {
      gameInfos.value = apiItems;
    }

    // 保存当前顺序
    saveOrder();
  }

  // 保存顺序到本地存储
  void saveOrder() {
    final jsonData = json.encode(
      gameInfos.map((item) => item.toJson()).toList(),
    );
    config.saveGameCenterData(jsonData);
  }

  // 打开会议
  void openMeeting(String? url) async {
    if (url == null || url.isEmpty) return;

    /// url已帶有meeting id
    var conf = Get.find<AppConfigService>();
    var nickname = conf.getMySelfDisplayName();
    var userId = conf.getUserNameWithoutDomain();
    nickname = Uri.encodeFull(nickname);
    userId = Uri.encodeFull(userId);
    String meetingUrl = '$url&userID=$userId&userName=$nickname';
    MeetingHelper().startMeeting(
      meetingUrl,
    );
  }

  // 点赞
  void toggleLike(int gameIndex, int articleIndex) {
    var article = gameInfos[gameIndex].articles![articleIndex];
    article.likeStatus.value = !article.likeStatus.value;
    if (article.likeStatus.value) {
      article.likeCount.value = article.likeCount.value + 1;
    } else {
      article.likeCount.value = article.likeCount.value - 1;
    }

    // 触发UI更新
    gameInfos.refresh();
  }

  /// 拖拽重新排序
  void reorderGame(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1; // 重要：调整索引
    }
    if (oldIndex == selectedGameIndex.value) {
      selectedGameIndex.value = newIndex;
    } else if (oldIndex > selectedGameIndex.value && newIndex <= selectedGameIndex.value) {
      selectedGameIndex.value += 1;
    } else if (oldIndex < selectedGameIndex.value && newIndex >= selectedGameIndex.value) {
      selectedGameIndex.value -= 1;
    }
    final item = gameInfos.removeAt(oldIndex);
    gameInfos.insert(newIndex, item);
    gameInfos.refresh();

    // 保存新的顺序
    saveOrder();
  }

  // 刷新数据
  Future<void> refresh() async {
    await fetchDataFromAPI();
  }

  Article getArticle(int gameIndex, int articleIndex) {
    return gameInfos[gameIndex].articles![articleIndex];
  }

  /// 排序文章
  void sortArticles(List<Article> articles) {
    articles.sort((a, b) {
      // 首先按isPin排序，true在前
      if (a.isPin! && !b.isPin!) return -1;
      if (!a.isPin! && b.isPin!) return 1;

      // 如果isPin相同，按日期排序（越近越靠前）
      DateTime dateA = DateTime.parse(a.createdAt!.replaceAll('/', '-'));
      DateTime dateB = DateTime.parse(b.createdAt!.replaceAll('/', '-'));

      return dateB.compareTo(dateA); // 降序排列，最新的在前
    });
  }

  /// 设置置顶
  void setArticleIsPin(int gameIndex, int articleIndex, bool isPin) {
    gameInfos[gameIndex].articles![articleIndex].isPin = isPin;
    sortArticles(gameInfos[gameIndex].articles!);
    gameInfos[gameIndex].articles!.refresh();
  }

  void onCreatePostTap() async {
    await openCreatePostView();
    // if (result == null) return;
    // _fetchPosts(); // 创建新帖 成功，刷新页面
  }

  Future<void> openCreatePostView() async {
    final result = await Get.toNamed(
      Routes.CreatePostView,
      arguments: {
        "is_article": true,
        "game_info": gameInfos[selectedGameIndex.value],
        // "current_user": currentUser,
        // "group_id": _groupId,
        // "post": post, // 只有编辑帖，才会有
      },
    );
    // return result != null ? result as Post : null;
  }
}

class GameInfo {
  final String id;
  final String name;
  final String? image;
  final String? description;
  final List<SquareBannerBean> banners;
  final String? meetingUrl;
  final RxList<Article>? articles;

  GameInfo({
    required this.id,
    required this.name,
    this.image,
    this.description,
    required this.banners,
    this.meetingUrl,
    this.articles,
  });

  factory GameInfo.fromJson(Map<String, dynamic> json) {
    return GameInfo(
      id: json['id'],
      name: json['name'],
      image: json['image'],
      description: json['description'],
      banners: (json['banners'] as List<dynamic>).map((item) => SquareBannerBean.fromJson(item)).toList(),
      meetingUrl: json['meetingUrl'],
      articles: (json['articles'] as List<dynamic>).map((item) => Article.fromJson(item)).toList().obs,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'description': description,
      'banners': banners.map((item) => item.toJson()).toList(),
      'meetingUrl': meetingUrl,
      'articles': articles?.map((item) => item.toJson()).toList(),
    };
  }
}

class Article {
  String? id;
  String? title;
  String? content;
  String? createdAt;
  bool? isMember;
  bool? isPin;
  RxBool likeStatus; // true: like, false: unlike
  RxInt likeCount;
  // int? dislikeCount;
  List<String>? imageUrls; // max 9
  String? videoUrl;
  String? videoThumbnail;
  double? videoThumbnailRatio;

  Article({
    this.id,
    this.title,
    this.content,
    this.createdAt,
    this.isMember,
    this.isPin,
    // this.likeStatus,
    // this.likeCount,
    // this.dislikeCount,
    bool? likeStatus,
    int? likeCount,
    this.imageUrls,
    this.videoUrl,
    this.videoThumbnail,
    this.videoThumbnailRatio,
  })  : likeStatus = RxBool(likeStatus ?? false),
        likeCount = RxInt(likeCount ?? 0);

  factory Article.fromJson(Map<String, dynamic> json) {
    return Article(
      id: json['id']?.toString(),
      title: json['title']?.toString(),
      content: json['content']?.toString(),
      createdAt: json['createdAt']?.toString(),
      isMember: json['isMember'] as bool?,
      isPin: json['isPin'] as bool?,
      likeStatus: json['likeStatus'] as bool?,
      likeCount: json['likeCount'] as int?,
      imageUrls: json['imageUrls'] != null ? List<String>.from(json['imageUrls'].map((x) => x.toString())) : null,
      videoUrl: json['videoUrl']?.toString(),
      videoThumbnail: json['videoThumbnail']?.toString(),
      videoThumbnailRatio: json['videoThumbnailRatio']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'createdAt': createdAt,
      'isMember': isMember,
      'isPin': isPin,
      'likeStatus': likeStatus.value,
      'likeCount': likeCount.value,
      'imageUrls': imageUrls,
      'videoUrl': videoUrl,
      'videoThumbnail': videoThumbnail,
      'videoThumbnailRatio': videoThumbnailRatio,
    };
  }
}
