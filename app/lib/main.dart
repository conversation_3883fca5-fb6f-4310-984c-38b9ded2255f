/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 09:59:27
 * @Description  : 程序的入口文件，这里可以初始化全局的状态
 * 
 * @LastEditors  : Da<PERSON>
 * @LastEditTime : 2022-07-20 12:58:11
 * @FilePath     : /flutter_metatel/lib/main.dart
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_metatel/app/data/events/events.dart';
import 'package:flutter_metatel/app/data/services/event_service.dart';
import 'package:flutter_metatel/app/data/services/translation_service.dart';
import 'package:flutter_metatel/app/modules/account/account_service.dart';
import 'package:flutter_metatel/core/utils/device_util.dart';
import 'package:flutter_metatel/core/utils/events_bus.dart';
import 'package:flutter_metatel/core/utils/language_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'app/data/services/biometrics_service.dart';
import 'app/data/services/channel_service.dart';
import 'app/data/services/chatio_service.dart';
import 'app/data/services/config_service.dart';
import 'app/data/services/database_service.dart';
import 'app/data/services/down_loads_service.dart';
import 'app/data/services/emoji_manage_service.dart';
import 'app/data/services/group_service.dart';
import 'app/data/services/headset_service.dart';
import 'app/data/services/network_connect_service.dart';
import 'app/data/services/notification_service.dart';
import 'app/data/services/probe_service.dart';
import 'app/data/services/push_service.dart';
import 'app/data/services/secure_store_service.dart';
import 'app/data/services/sip_service.dart';
import 'app/data/services/user_status_service.dart';
import 'app/data/services/webrtc_service.dart';
import 'app/modules/splash/splash_binging.dart';
import 'core/languages/l_hw.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/util.dart';
import 'core/values/colors.dart';
import 'core/values/config.dart';
import 'routes/pages.dart';

Future<void> main() async {
  // await GetStorage.init();
  await Get.putAsync(() => EventService().init());
  await Get.putAsync(() => SipService().init());
  await Get.putAsync(() => AppConfigService().init());
  await Get.putAsync(() => SecureStoreService().init());
  await Get.putAsync(() => DatabaseService().init(), permanent: true);
  await Get.putAsync(() => ChatioService().init());
  await Get.putAsync(() => NetWorkConnectService().init(), permanent: true);
  await Get.putAsync(() => WebRtcService().init());
  await Get.putAsync(() => HeadSetService().init());
  await Get.putAsync(() => NotificationService().init());
  await Get.putAsync(() => ChannelService().init());
  await Get.putAsync(() => GroupService().init());
  await Get.putAsync(() => BiometricsService().configBiometricsType());
  await Get.putAsync(() => AccountService().init());
  await Get.putAsync(() => PushService().init());
  Get.putAsync(() => ProbeService().init());
  Get.putAsync(() => DownLoadService().init());
  Get.putAsync(() => EmojiManageService().init());
  Get.putAsync(() => UserStatusService().init(), permanent: true);

  WidgetsFlutterBinding.ensureInitialized(); //不加这个强制横/竖屏会报错
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);

  SystemChrome.setPreferredOrientations([
    // 强制竖屏
    DeviceOrientation.portraitUp,
  ]);

  // await FlutterDownloader.initialize(
  //     debug:
  //         true, // optional: set to false to disable printing logs to console (default: true)
  //     ignoreSsl:
  //         true // option: set to false to disable working with http links (default: false)
  //     );
  var deviceLocale = Get.deviceLocale;

  // AppPages.pages.addAll(WalletAppPages.pages);
  // debugProfileBuildsEnabled = true;
  // HttpOverrides.global = MyHttpOverrides();

  var translations = await TranslationService().init();

  FlutterBugly.postCatchedException(
    () {
      run(translations);
      FlutterBugly.init(
        androidAppId: "1ede86577e",
      );
    },
    debugUpload: false,
  );

  // _configEasyLoading();
}

void _configEasyLoading() {
  EasyLoading.instance
    ..indicatorType = EasyLoadingIndicatorType.ring
    ..loadingStyle = EasyLoadingStyle.custom
    ..backgroundColor = AppColors.colorFF928A7C
    ..indicatorColor = AppColors.colorFFEEEBE4
    ..textColor = AppColors.colorFFEEEBE4
    ..progressColor = AppColors.colorFFEEEBE4
    ..radius = 20.r
    ..textStyle = TextStyle(fontSize: 16.sp, color: AppColors.colorFFEEEBE4, fontWeight: FontWeight.bold);
}

run(translations) async {
  final portraitDesignSize = const Size(428, 926);
  final landscapeDesignSize = const Size(926, 428);
  final aspectRatioThreshold = 0.85;
  var designSize;
  runApp(
    ScreenUtilInit(
        // designSize: const Size(375, 667),
        minTextAdapt: true,
        splitScreenMode: true,
        rebuildFactor: (old, data) {
          /// 当页面大小改变时，根据宽高比，设置designSize
          if (old.size != data.size) {
            double widthHeightRatio = data.size.width / data.size.height;
            if (widthHeightRatio > aspectRatioThreshold) {
              if (designSize != landscapeDesignSize) {
                designSize = landscapeDesignSize;
                return true;
              }
            } else {
              if (designSize != portraitDesignSize) {
                designSize = portraitDesignSize;
                return true;
              }
            }
          }
          return false;
        },
        builder: (context, child) {
          // 根据宽高比，设置第一次的designSize
          designSize ??= MediaQuery.sizeOf(context).width / MediaQuery.sizeOf(context).height > aspectRatioThreshold
              ? landscapeDesignSize
              : portraitDesignSize;
          // Set design size based on orientation
          ScreenUtil.init(
            context,
            designSize: designSize,
            minTextAdapt: true,
            splitScreenMode: true,
          );
          return GetMaterialApp(
            localizationsDelegates: const [
              RefreshLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate
            ],
            // supportedLocales: TranslationService().languageList.map((e) => Locale(e.split('_')[0], e.split('_')[1])).toList(),
            supportedLocales: [(Locale('zh', 'CN'))],
            debugShowCheckedModeBanner: false,
            themeMode: ThemeMode.light,
            theme: appThemeData,
            initialRoute: Routes.SPLASH,
            translations: translations,
            title: getAppName() ?? '',
            // your translations
            // locale: LanguageUtil.getCatheLanguageLocale(),
            locale: Locale('zh', 'CN'),
            defaultTransition: Transition.cupertino,
            routingCallback: _runTingBack,
            // translations will be displayed in that locale
            // fallbackLocale: LanguageUtil.getCatheLanguageLocale(),
            fallbackLocale: Locale('zh', 'CN'),
            // fallback locale
            initialBinding: SplashBinding(),
            getPages: AppPages.pages,
            navigatorObservers: [FlutterSmartDialog.observer],
            builder: FlutterSmartDialog.init(builder: (context, widget) {
              widget = EasyLoading.init()(context, widget);
              _configEasyLoading();
              // 禁用系统字体缩放
              return MediaQuery(
                  data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
                  child: FlutterSmartDialog(
                    child: widget,
                  ));
            }),
            localeListResolutionCallback: (locales, supportedLocales) {
              if (Config.appIsBackground && !DeviceUtil.isIOS()) {
                Get.find<EventBus>().fire(UpdateLanguageEvent());
              }
              return LanguageUtil.getCatheLanguageLocale();
            },
          );
        }),
  );
}

String? getAppName() {
  return currentLanguageIsSimpleChinese() ? LocalesHw.zh_CN['app_name'] : LocalesHw.en_US['app_name'];
}

void _runTingBack(Routing? rout) {
  // AppLogger.d(
  //     '_runTingBack==previous=${rout?.previous}||||isBack=${rout?.isBack}||||current=${rout?.current}||||removed=${rout?.removed}');
}
